# Consolidated Email Notification Changes

## Overview
Modified the invoice/bill due notification system to send consolidated emails with tables instead of individual emails for each invoice/bill.

## Problem Solved
**Before**: The system sent one email per invoice/bill, resulting in multiple emails per day (e.g., if 10 invoices were due, 10 separate emails were sent).

**After**: The system now sends one consolidated email per day containing a table with all invoices/bills due on that date.

## Changes Made

### 1. Updated Notification Logic (`models/account_move.py`)

#### Modified Methods:
- `_send_due_notifications()`: Updated template references to use consolidated templates
- `_send_notifications_for_date()`: Complete rewrite to send consolidated emails
- Added `_prepare_consolidated_moves_data()`: New method to prepare data for email templates

#### Key Changes:
- Changed from individual `template.send_mail(move.id)` calls to single consolidated email
- Added data preparation for table format
- Bulk update of notification flags for all processed moves
- Enhanced error handling and logging

### 2. New Email Templates

#### Created 4 New Consolidated Templates:

1. **Invoice Due Date Notification (Consolidated)**
   - Template ID: `email_template_invoice_due_notification_consolidated`
   - File: `data/consolidated_mail_templates.xml`
   - Features: Professional table with invoice details, summary statistics

2. **Invoice Due Tomorrow Notification (Consolidated)**
   - Template ID: `email_template_invoice_one_day_due_consolidated`
   - File: `data/consolidated_mail_templates.xml`
   - Features: Urgent styling, red color scheme, immediate action items

3. **Bill Due Date Notification (Consolidated)**
   - Template ID: `email_template_bill_due_notification_consolidated`
   - File: `data/consolidated_bill_templates.xml`
   - Features: Blue color scheme, payment-focused messaging

4. **Bill Due Tomorrow Notification (Consolidated)**
   - Template ID: `email_template_bill_one_day_due_consolidated`
   - File: `data/consolidated_bill_templates.xml`
   - Features: Urgent payment processing alerts, critical action items

### 3. Template Features

#### Table Columns:
**For Invoices:**
- Invoice Number
- Customer
- Invoice Date
- Due Date
- Total Amount
- Amount Due
- Status

**For Bills:**
- Bill Number
- Vendor
- Bill Date
- Due Date
- Total Amount
- Amount Due
- Status

#### Visual Enhancements:
- Alternating row colors for better readability
- Color-coded headers (Purple for invoices, Blue for bills, Red for urgent)
- Status indicators with appropriate colors
- Summary boxes with key statistics
- Professional company branding

### 4. Data Structure

#### Context Variables Available in Templates:
- `moves_data`: Array of invoice/bill data
- `moves_count`: Total number of items
- `target_date`: Due date for the batch
- `notification_type`: 'invoice' or 'bill'

#### Individual Move Data:
- `name`: Invoice/Bill number
- `partner_name`: Customer/Vendor name
- `invoice_date`: Document date
- `invoice_date_due`: Due date
- `amount_total`: Total amount
- `amount_residual`: Outstanding amount
- `currency_symbol`: Currency symbol
- `payment_state`: Payment status
- `partner_type`: 'Customer' or 'Vendor'
- `document_type`: 'Invoice' or 'Bill'

## Benefits

### 1. Reduced Email Volume
- **Before**: 10 invoices = 10 emails
- **After**: 10 invoices = 1 email

### 2. Better Overview
- All items due on the same date in one view
- Easy to prioritize and plan actions
- Summary statistics for quick assessment

### 3. Professional Presentation
- Clean table format
- Consistent branding
- Mobile-responsive design

### 4. Improved Efficiency
- Bulk processing of notifications
- Single email template rendering
- Reduced server load

## Backward Compatibility

The original individual email templates are preserved:
- `email_template_invoice_due_notification`
- `email_template_invoice_one_day_due`
- `email_template_bill_due_notification`
- `email_template_bill_one_day_due`

To revert to individual emails, simply change the template references in `_send_due_notifications()` method.

## Testing

### Manual Testing Steps:
1. Create test invoices/bills with due dates
2. Configure notification settings
3. Run cron jobs manually:
   ```python
   env['account.move']._cron_send_invoice_due_notifications()
   env['account.move']._cron_send_bill_due_notifications()
   ```
4. Verify consolidated emails are sent
5. Check that all items are included in the table
6. Confirm notification flags are updated for all items

### Test Scenarios:
- Single invoice/bill due
- Multiple invoices/bills due on same date
- Mixed payment states (not_paid, partial)
- Different currencies
- Long customer/vendor names
- Large amounts

## Configuration

No additional configuration required. The system uses existing settings:
- `invoice_due_notification_days`
- `bill_due_notification_days`
- `invoice_notification_email`
- `bill_notification_email`
- `enable_invoice_notifications`
- `enable_bill_notifications`

## Files Modified/Added

### Modified:
- `models/account_move.py`
- `__manifest__.py`

### Added:
- `data/consolidated_mail_templates.xml`
- `data/consolidated_bill_templates.xml`
- `CONSOLIDATED_EMAIL_CHANGES.md` (this file)

## Future Enhancements

Potential improvements:
1. Add total amount summaries in email footer
2. Group by currency for multi-currency environments
3. Add filtering options (by amount, customer type, etc.)
4. Include aging analysis in the email
5. Add export functionality (CSV/Excel attachment)
