<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Consolidated Email Template for Bill Due Date Notification -->
        <record id="email_template_bill_due_notification_consolidated" model="mail.template">
            <field name="name">Bill Due Date Notification (Consolidated)</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="subject">Bill Payment Due Reminder - {{ ctx.get('moves_count', 0) }} Bill(s) Due on {{ ctx.get('target_date', 'N/A') }}</field>
            <field name="email_from">{{ (object.company_id.email or user.email) }}</field>
            <field name="email_to">{{
                object.env['ir.config_parameter'].sudo().get_param('invoice_bill_due_notification.bill_notification_email',
                '') }}
            </field>
            <field name="description">Consolidated notification sent to payables team for upcoming bill due dates</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px; font-family: 'Lucida Grande', Ubuntu, Arial, Verdana, sans-serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFFFFF;">
                    <table border="0" cellpadding="0" cellspacing="0"
                           style="padding: 16px; background-color: white; color: #515166; width: 100%; border-collapse:separate;">
                        <tr>
                            <td align="center">
                                <table border="0" cellpadding="0" cellspacing="0" width="590"
                                       style="padding: 16px; background-color: white; color: #515166; border-collapse:separate;">
                                    <tbody>
                                        <!-- Header -->
                                        <tr>
                                            <td align="center" style="min-width: 590px;">
                                                <table border="0" cellpadding="0" cellspacing="0" width="590"
                                                       style="min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;">
                                                    <tr>
                                                        <td valign="middle">
                                                            <span style="font-size: 20px; color: #17a2b8; font-weight: bold;">
                                                                Bill Payment Due Reminder
                                                            </span>
                                                        </td>
                                                        <td valign="middle" align="right">
                                                            <img t-if="object.company_id.logo"
                                                                 t-att-src="image_data_uri(object.company_id.logo)"
                                                                 style="padding: 0px; margin: 0px; height: auto; width: 80px;"
                                                                 alt="Company Logo"/>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="2" style="text-align:center;">
                                                            <hr width="100%"
                                                                style="background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;"/>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <!-- Content -->
                                        <tr>
                                            <td align="center" style="min-width: 590px;">
                                                <table border="0" cellpadding="0" cellspacing="0" width="590"
                                                       style="min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;">
                                                    <tr>
                                                        <td valign="top" style="font-size: 13px;">
                                                            <div>
                                                                <p>Dear Payables Team,</p>
                                                                <p>This is a reminder that the following <strong>{{ ctx.get('moves_count', 0) }} vendor bill(s)</strong> are due for payment on <strong>{{ ctx.get('target_date', 'N/A') }}</strong>:</p>

                                                                <!-- Consolidated Bill Table -->
                                                                <table border="1" cellpadding="8" cellspacing="0"
                                                                       style="border-collapse: collapse; width: 100%; margin: 16px 0;">
                                                                    <thead>
                                                                        <tr style="background-color: #17a2b8; color: white;">
                                                                            <th style="text-align: left; border: 1px solid #dee2e6; padding: 12px;">Bill Number</th>
                                                                            <th style="text-align: left; border: 1px solid #dee2e6; padding: 12px;">Vendor</th>
                                                                            <th style="text-align: left; border: 1px solid #dee2e6; padding: 12px;">Bill Date</th>
                                                                            <th style="text-align: left; border: 1px solid #dee2e6; padding: 12px;">Due Date</th>
                                                                            <th style="text-align: right; border: 1px solid #dee2e6; padding: 12px;">Total Amount</th>
                                                                            <th style="text-align: right; border: 1px solid #dee2e6; padding: 12px;">Amount Due</th>
                                                                            <th style="text-align: center; border: 1px solid #dee2e6; padding: 12px;">Status</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        <t t-foreach="ctx.get('moves_data', [])" t-as="move_data">
                                                                            <tr t-att-style="'background-color: #f8f9fa;' if move_data_index % 2 == 0 else ''">
                                                                                <td style="border: 1px solid #dee2e6; padding: 8px;">
                                                                                    <strong t-out="move_data.get('name', 'N/A')">BILL/2024/0001</strong>
                                                                                </td>
                                                                                <td style="border: 1px solid #dee2e6; padding: 8px;" t-out="move_data.get('partner_name', 'N/A')">Vendor Name</td>
                                                                                <td style="border: 1px solid #dee2e6; padding: 8px;" t-out="move_data.get('invoice_date', 'N/A')">2024-01-01</td>
                                                                                <td style="border: 1px solid #dee2e6; padding: 8px;" t-out="move_data.get('invoice_date_due', 'N/A')">2024-01-15</td>
                                                                                <td style="border: 1px solid #dee2e6; padding: 8px; text-align: right;">
                                                                                    <span t-out="move_data.get('currency_symbol', '')">$</span><span t-out="'{:,.2f}'.format(move_data.get('amount_total', 0))">1,000.00</span>
                                                                                </td>
                                                                                <td style="border: 1px solid #dee2e6; padding: 8px; text-align: right;">
                                                                                    <span t-out="move_data.get('currency_symbol', '')">$</span><span t-out="'{:,.2f}'.format(move_data.get('amount_residual', 0))">1,000.00</span>
                                                                                </td>
                                                                                <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">
                                                                                    <span t-att-style="'color: #dc3545; font-weight: bold;' if move_data.get('payment_state') == 'not_paid' else 'color: #ffc107; font-weight: bold;'"
                                                                                          t-out="move_data.get('payment_state', 'N/A')">Not Paid</span>
                                                                                </td>
                                                                            </tr>
                                                                        </t>
                                                                    </tbody>
                                                                </table>

                                                                <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 12px; margin: 16px 0; border-radius: 4px;">
                                                                    <p style="margin: 0; color: #0c5460; font-weight: bold;">
                                                                        💳 Summary: {{ ctx.get('moves_count', 0) }} bill(s) require payment processing.
                                                                    </p>
                                                                </div>

                                                                <p><strong>Next Steps:</strong></p>
                                                                <ul>
                                                                    <li>Review each bill for payment approval</li>
                                                                    <li>Verify vendor details and payment terms</li>
                                                                    <li>Process payments according to company policy</li>
                                                                    <li>Update payment status in the system</li>
                                                                </ul>

                                                                <p>Please ensure timely payment to maintain good vendor relationships and avoid late fees.</p>

                                                                <p>Best regards,<br/>
                                                                   Automated Notification System</p>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <!-- Footer -->
                                        <tr>
                                            <td align="center" style="min-width: 590px;">
                                                <table border="0" cellpadding="0" cellspacing="0" width="590"
                                                       style="min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;">
                                                    <tr>
                                                        <td valign="top" style="font-size: 10px; color: #999999;">
                                                            <hr width="100%"
                                                                style="background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;"/>
                                                            <p>This is an automated notification from your Bill Management System.</p>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </table>
                </div>
            </field>
        </record>

        <!-- Consolidated Email Template for Bill One Day Before Due Date -->
        <record id="email_template_bill_one_day_due_consolidated" model="mail.template">
            <field name="name">Bill Due Tomorrow Notification (Consolidated)</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="subject">URGENT: {{ ctx.get('moves_count', 0) }} Bill(s) Payment Due Tomorrow - {{ ctx.get('target_date', 'N/A') }}</field>
            <field name="email_from">{{ (object.company_id.email or user.email) }}</field>
            <field name="email_to">{{
                object.env['ir.config_parameter'].sudo().get_param('invoice_bill_due_notification.bill_notification_email',
                '') }}
            </field>
            <field name="description">Urgent consolidated notification sent one day before bill due date</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px; font-family: 'Lucida Grande', Ubuntu, Arial, Verdana, sans-serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFFFFF;">
                    <table border="0" cellpadding="0" cellspacing="0"
                           style="padding: 16px; background-color: white; color: #515166; width: 100%; border-collapse:separate;">
                        <tr>
                            <td align="center">
                                <table border="0" cellpadding="0" cellspacing="0" width="590"
                                       style="padding: 16px; background-color: white; color: #515166; border-collapse:separate;">
                                    <tbody>
                                        <!-- Header -->
                                        <tr>
                                            <td align="center" style="min-width: 590px;">
                                                <table border="0" cellpadding="0" cellspacing="0" width="590"
                                                       style="min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;">
                                                    <tr>
                                                        <td valign="middle">
                                                            <span style="font-size: 20px; color: #dc3545; font-weight: bold;">
                                                                🚨 URGENT: Bills Payment Due Tomorrow
                                                            </span>
                                                        </td>
                                                        <td valign="middle" align="right">
                                                            <img t-if="object.company_id.logo"
                                                                 t-att-src="image_data_uri(object.company_id.logo)"
                                                                 style="padding: 0px; margin: 0px; height: auto; width: 80px;"
                                                                 alt="Company Logo"/>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="2" style="text-align:center;">
                                                            <hr width="100%"
                                                                style="background-color:rgb(220,53,69);border:medium none;clear:both;display:block;font-size:0px;min-height:2px;line-height:0; margin: 16px 0px 16px 0px;"/>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <!-- Content -->
                                        <tr>
                                            <td align="center" style="min-width: 590px;">
                                                <table border="0" cellpadding="0" cellspacing="0" width="590"
                                                       style="min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;">
                                                    <tr>
                                                        <td valign="top" style="font-size: 13px;">
                                                            <div>
                                                                <p><strong style="color: #dc3545;">URGENT PAYMENT REQUIRED</strong></p>
                                                                <p>Dear Payables Team,</p>
                                                                <p>The following <strong style="color: #dc3545;">{{ ctx.get('moves_count', 0) }} vendor bill(s)</strong> payment is <strong style="color: #dc3545;">due tomorrow ({{ ctx.get('target_date', 'N/A') }})</strong>. Immediate action is required:</p>

                                                                <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 12px; margin: 16px 0; border-radius: 4px;">
                                                                    <p style="margin: 0; color: #856404; font-weight: bold;">
                                                                        ⚠️ These bills will be overdue if not paid by tomorrow!
                                                                    </p>
                                                                </div>

                                                                <!-- Urgent Bill Table -->
                                                                <table border="1" cellpadding="8" cellspacing="0"
                                                                       style="border-collapse: collapse; width: 100%; margin: 16px 0;">
                                                                    <thead>
                                                                        <tr style="background-color: #dc3545; color: white;">
                                                                            <th style="text-align: left; border: 1px solid #dee2e6; padding: 12px;">Bill Number</th>
                                                                            <th style="text-align: left; border: 1px solid #dee2e6; padding: 12px;">Vendor</th>
                                                                            <th style="text-align: left; border: 1px solid #dee2e6; padding: 12px;">Bill Date</th>
                                                                            <th style="text-align: left; border: 1px solid #dee2e6; padding: 12px;">Due Date</th>
                                                                            <th style="text-align: right; border: 1px solid #dee2e6; padding: 12px;">Amount Due</th>
                                                                            <th style="text-align: center; border: 1px solid #dee2e6; padding: 12px;">Status</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        <t t-foreach="ctx.get('moves_data', [])" t-as="move_data">
                                                                            <tr t-att-style="'background-color: #ffe6e6;' if move_data_index % 2 == 0 else 'background-color: #fff0f0;'">
                                                                                <td style="border: 1px solid #dee2e6; padding: 8px;">
                                                                                    <strong t-out="move_data.get('name', 'N/A')" style="color: #dc3545;">BILL/2024/0001</strong>
                                                                                </td>
                                                                                <td style="border: 1px solid #dee2e6; padding: 8px;" t-out="move_data.get('partner_name', 'N/A')">Vendor Name</td>
                                                                                <td style="border: 1px solid #dee2e6; padding: 8px;" t-out="move_data.get('invoice_date', 'N/A')">2024-01-01</td>
                                                                                <td style="border: 1px solid #dee2e6; padding: 8px; color: #dc3545; font-weight: bold;" t-out="move_data.get('invoice_date_due', 'N/A')">2024-01-15</td>
                                                                                <td style="border: 1px solid #dee2e6; padding: 8px; text-align: right; color: #dc3545; font-weight: bold;">
                                                                                    <span t-out="move_data.get('currency_symbol', '')">$</span><span t-out="'{:,.2f}'.format(move_data.get('amount_residual', 0))">1,000.00</span>
                                                                                </td>
                                                                                <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">
                                                                                    <span style="color: #dc3545; font-weight: bold;" t-out="move_data.get('payment_state', 'N/A')">Not Paid</span>
                                                                                </td>
                                                                            </tr>
                                                                        </t>
                                                                    </tbody>
                                                                </table>

                                                                <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 12px; margin: 16px 0; border-radius: 4px;">
                                                                    <p style="margin: 0; color: #721c24; font-weight: bold;">
                                                                        🚨 CRITICAL: {{ ctx.get('moves_count', 0) }} bill(s) require IMMEDIATE payment processing!
                                                                    </p>
                                                                </div>

                                                                <p><strong style="color: #dc3545;">IMMEDIATE ACTION REQUIRED:</strong></p>
                                                                <ul>
                                                                    <li><strong>Process payments TODAY</strong> to avoid late fees</li>
                                                                    <li><strong>Verify payment details</strong> and vendor information</li>
                                                                    <li><strong>Obtain necessary approvals</strong> for payment processing</li>
                                                                    <li><strong>Schedule payments</strong> for immediate execution</li>
                                                                    <li><strong>Notify vendors</strong> of payment status if needed</li>
                                                                </ul>

                                                                <p style="color: #dc3545; font-weight: bold;">These bills will become overdue tomorrow. Please prioritize payment processing immediately to maintain vendor relationships and avoid penalties.</p>

                                                                <p>Best regards,<br/>
                                                                   Automated Notification System</p>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <!-- Footer -->
                                        <tr>
                                            <td align="center" style="min-width: 590px;">
                                                <table border="0" cellpadding="0" cellspacing="0" width="590"
                                                       style="min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;">
                                                    <tr>
                                                        <td valign="top" style="font-size: 10px; color: #999999;">
                                                            <hr width="100%"
                                                                style="background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;"/>
                                                            <p>This is an automated urgent notification from your Bill Management System.</p>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </table>
                </div>
            </field>
        </record>

    </data>
</odoo>
