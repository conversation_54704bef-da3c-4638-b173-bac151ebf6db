<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- <PERSON>ron job for Invoice Due Date Notifications -->
        <record id="ir_cron_invoice_due_notification" model="ir.cron">
            <field name="name">Invoice Due Date Notifications</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="state">code</field>
            <field name="code">model._cron_send_invoice_due_notifications()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="active" eval="False"/>
        </record>

        <!-- Cron job for Bill Due Date Notifications -->
        <record id="ir_cron_bill_due_notification" model="ir.cron">
            <field name="name">Bill Due Date Notifications</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="state">code</field>
            <field name="code">model._cron_send_bill_due_notifications()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="active" eval="False"/>
        </record>

    </data>
</odoo>
