<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- <PERSON>ron job for Invoice Due Date Notifications -->
        <record id="ir_cron_invoice_due_notification" model="ir.cron">
            <field name="name">Invoice Due Date Notifications</field>
            <field name="model_id" ref="model_invoice_bill_notification"/>
            <field name="state">code</field>
            <field name="code">model._cron_send_invoice_due_notifications()</field>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="nextcall" eval="(DateTime.now().replace(hour=8, minute=0) + timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')" />
            <field name="active" eval="True"/>
            <field name="doall" eval="False"/>
            <field name="numbercall">-1</field>
        </record>
        
        <!-- <PERSON>ron job for Bill Due Date Notifications -->
        <record id="ir_cron_bill_due_notification" model="ir.cron">
            <field name="name">Bill Due Date Notifications</field>
            <field name="model_id" ref="model_invoice_bill_notification"/>
            <field name="state">code</field>
            <field name="code">model._cron_send_bill_due_notifications()</field>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="nextcall" eval="(DateTime.now().replace(hour=8, minute=30) + timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')" />
            <field name="active" eval="True"/>
            <field name="doall" eval="False"/>
            <field name="numbercall">-1</field>
        </record>
        
    </data>
</odoo>
