<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Email Template for Invoice Due Date Notification -->
        <record id="email_template_invoice_due_notification" model="mail.template">
            <field name="name">Invoice Due Date Notification</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="subject">Invoice Due Date Reminder - {{ object.name or 'N/A' }} (Due: {{
                object.invoice_date_due }})
            </field>
            <field name="email_from">{{ (object.company_id.email or user.email) }}</field>
            <field name="email_to">{{
                object.env['ir.config_parameter'].sudo().get_param('invoice_bill_due_notification.invoice_notification_email',
                '') }}
            </field>
            <field name="description">Notification sent to payables team for upcoming invoice due dates</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px; font-family: 'Lucida Grande', Ubuntu, Arial, Verdana, sans-serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFFFFF;">
                    <table border="0" cellpadding="0" cellspacing="0"
                           style="padding: 16px; background-color: white; color: #515166; width: 100%; border-collapse:separate;">
                        <tr>
                            <td align="center">
                                <table border="0" cellpadding="0" cellspacing="0" width="590"
                                       style="padding: 16px; background-color: white; color: #515166; border-collapse:separate;">
                                    <tbody>
                                        <!-- Header -->
                                        <tr>
                                            <td align="center" style="min-width: 590px;">
                                                <table border="0" cellpadding="0" cellspacing="0" width="590"
                                                       style="min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;">
                                                    <tr>
                                                        <td valign="middle">
                                                            <span style="font-size: 20px; color: #875A7B; font-weight: bold;">
                                                                Invoice Due Date Reminder
                                                            </span>
                                                        </td>
                                                        <td valign="middle" align="right">
                                                            <img t-if="object.company_id.logo"
                                                                 t-att-src="image_data_uri(object.company_id.logo)"
                                                                 style="padding: 0px; margin: 0px; height: auto; width: 80px;"
                                                                 alt="Company Logo"/>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="2" style="text-align:center;">
                                                            <hr width="100%"
                                                                style="background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;"/>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <!-- Content -->
                                        <tr>
                                            <td align="center" style="min-width: 590px;">
                                                <table border="0" cellpadding="0" cellspacing="0" width="590"
                                                       style="min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;">
                                                    <tr>
                                                        <td valign="top" style="font-size: 13px;">
                                                            <div>
                                                                <p>Dear Payables Team,</p>
                                                                <p>This is a reminder that the following customer
                                                                    invoice is approaching its due date:
                                                                </p>

                                                                <table border="1" cellpadding="8" cellspacing="0"
                                                                       style="border-collapse: collapse; width: 100%; margin: 16px 0;">
                                                                    <tr style="background-color: #f8f9fa;">
                                                                        <th style="text-align: left; border: 1px solid #dee2e6;">
                                                                            Invoice Number
                                                                        </th>
                                                                        <td style="border: 1px solid #dee2e6;">
                                                                            <strong t-out="object.name or 'N/A'">
                                                                                INV/2024/0001
                                                                            </strong>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th style="text-align: left; border: 1px solid #dee2e6;">
                                                                            Customer
                                                                        </th>
                                                                        <td style="border: 1px solid #dee2e6;"
                                                                            t-out="object.partner_id.name or 'N/A'">
                                                                            Customer Name
                                                                        </td>
                                                                    </tr>
                                                                    <tr style="background-color: #f8f9fa;">
                                                                        <th style="text-align: left; border: 1px solid #dee2e6;">
                                                                            Invoice Date
                                                                        </th>
                                                                        <td style="border: 1px solid #dee2e6;"
                                                                            t-out="object.invoice_date or 'N/A'">
                                                                            2024-01-01
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th style="text-align: left; border: 1px solid #dee2e6;">
                                                                            Due Date
                                                                        </th>
                                                                        <td style="border: 1px solid #dee2e6; color: #dc3545; font-weight: bold;"
                                                                            t-out="object.invoice_date_due or 'N/A'">
                                                                            2024-01-15
                                                                        </td>
                                                                    </tr>
                                                                    <tr style="background-color: #f8f9fa;">
                                                                        <th style="text-align: left; border: 1px solid #dee2e6;">
                                                                            Amount Total
                                                                        </th>
                                                                        <td style="border: 1px solid #dee2e6; font-weight: bold;">
                                                                            <span t-out="object.amount_total">1000.00
                                                                            </span>
                                                                            <span t-out="object.currency_id.symbol or object.currency_id.name">
                                                                                $
                                                                            </span>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th style="text-align: left; border: 1px solid #dee2e6;">
                                                                            Payment Status
                                                                        </th>
                                                                        <td style="border: 1px solid #dee2e6;"
                                                                            t-out="dict(object._fields['payment_state'].selection).get(object.payment_state, object.payment_state)">
                                                                            Not Paid
                                                                        </td>
                                                                    </tr>
                                                                </table>

                                                                <p style="margin-top: 16px;">
                                                                    <strong>Action Required:</strong>
                                                                    Please follow up with the customer for payment
                                                                    collection.
                                                                </p>

                                                                <p>
                                                                    Best regards,
                                                                    <br/>
                                                                    <span t-out="object.company_id.name">Your Company
                                                                    </span>
                                                                </p>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <!-- Footer -->
                                        <tr>
                                            <td align="center" style="min-width: 590px;">
                                                <table border="0" cellpadding="0" cellspacing="0" width="590"
                                                       style="min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;">
                                                    <tr>
                                                        <td valign="middle" align="left"
                                                            style="color: #787878; font-size: 11px;">
                                                            <span t-out="object.company_id.name">Your Company</span>
                                                            |
                                                            <span t-out="object.company_id.phone">****** 567 8900</span>
                                                            |
                                                            <span t-out="object.company_id.email"><EMAIL>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </table>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <!-- Email Template for Invoice One Day Before Due Date -->
        <record id="email_template_invoice_one_day_due" model="mail.template">
            <field name="name">Invoice Due Tomorrow Notification</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="subject">URGENT: Invoice Due Tomorrow - {{ object.name or 'N/A' }} (Due: {{
                object.invoice_date_due }})
            </field>
            <field name="email_from">{{ (object.company_id.email or user.email) }}</field>
            <field name="email_to">{{
                object.env['ir.config_parameter'].sudo().get_param('invoice_bill_due_notification.invoice_notification_email',
                '') }}
            </field>
            <field name="description">Urgent notification sent one day before invoice due date</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px; font-family: 'Lucida Grande', Ubuntu, Arial, Verdana, sans-serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFFFFF;">
                    <table border="0" cellpadding="0" cellspacing="0"
                           style="padding: 16px; background-color: white; color: #515166; width: 100%; border-collapse:separate;">
                        <tr>
                            <td align="center">
                                <table border="0" cellpadding="0" cellspacing="0" width="590"
                                       style="padding: 16px; background-color: white; color: #515166; border-collapse:separate;">
                                    <tbody>
                                        <!-- Header -->
                                        <tr>
                                            <td align="center" style="min-width: 590px;">
                                                <table border="0" cellpadding="0" cellspacing="0" width="590"
                                                       style="min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;">
                                                    <tr>
                                                        <td valign="middle">
                                                            <span style="font-size: 20px; color: #dc3545; font-weight: bold;">
                                                                URGENT: Invoice Due Tomorrow
                                                            </span>
                                                        </td>
                                                        <td valign="middle" align="right">
                                                            <img t-if="object.company_id.logo"
                                                                 t-att-src="image_data_uri(object.company_id.logo)"
                                                                 style="padding: 0px; margin: 0px; height: auto; width: 80px;"
                                                                 alt="Company Logo"/>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="2" style="text-align:center;">
                                                            <hr width="100%"
                                                                style="background-color:rgb(220,53,69);border:medium none;clear:both;display:block;font-size:0px;min-height:2px;line-height:0; margin: 16px 0px 16px 0px;"/>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <!-- Content -->
                                        <tr>
                                            <td align="center" style="min-width: 590px;">
                                                <table border="0" cellpadding="0" cellspacing="0" width="590"
                                                       style="min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;">
                                                    <tr>
                                                        <td valign="top" style="font-size: 13px;">
                                                            <div>
                                                                <p>
                                                                    <strong style="color: #dc3545;">URGENT ATTENTION
                                                                        REQUIRED
                                                                    </strong>
                                                                </p>
                                                                <p>Dear Payables Team,</p>
                                                                <p>The following customer invoice is <strong
                                                                        style="color: #dc3545;">due tomorrow</strong>.
                                                                    Immediate action is required:
                                                                </p>

                                                                <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 12px; margin: 16px 0; border-radius: 4px;">
                                                                    <p style="margin: 0; color: #856404; font-weight: bold;">
                                                                        ⚠️ This invoice will be overdue if not collected
                                                                        by tomorrow!
                                                                    </p>
                                                                </div>

                                                                <table border="1" cellpadding="8" cellspacing="0"
                                                                       style="border-collapse: collapse; width: 100%; margin: 16px 0;">
                                                                    <tr style="background-color: #f8f9fa;">
                                                                        <th style="text-align: left; border: 1px solid #dee2e6;">
                                                                            Invoice Number
                                                                        </th>
                                                                        <td style="border: 1px solid #dee2e6;">
                                                                            <strong t-out="object.name or 'N/A'">
                                                                                INV/2024/0001
                                                                            </strong>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th style="text-align: left; border: 1px solid #dee2e6;">
                                                                            Customer
                                                                        </th>
                                                                        <td style="border: 1px solid #dee2e6;"
                                                                            t-out="object.partner_id.name or 'N/A'">
                                                                            Customer Name
                                                                        </td>
                                                                    </tr>
                                                                    <tr style="background-color: #f8f9fa;">
                                                                        <th style="text-align: left; border: 1px solid #dee2e6;">
                                                                            Due Date
                                                                        </th>
                                                                        <td style="border: 1px solid #dee2e6; color: #dc3545; font-weight: bold; font-size: 14px;"
                                                                            t-out="object.invoice_date_due or 'N/A'">
                                                                            2024-01-15
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th style="text-align: left; border: 1px solid #dee2e6;">
                                                                            Amount Total
                                                                        </th>
                                                                        <td style="border: 1px solid #dee2e6; font-weight: bold; font-size: 14px;">
                                                                            <span t-out="object.amount_total">1000.00
                                                                            </span>
                                                                            <span t-out="object.currency_id.symbol or object.currency_id.name">
                                                                                $
                                                                            </span>
                                                                        </td>
                                                                    </tr>
                                                                </table>

                                                                <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 12px; margin: 16px 0; border-radius: 4px;">
                                                                    <p style="margin: 0; color: #0c5460; font-weight: bold;">
                                                                        IMMEDIATE ACTION REQUIRED:
                                                                    </p>
                                                                    <ul style="margin: 8px 0 0 20px; color: #0c5460;">
                                                                        <li>Contact customer immediately</li>
                                                                        <li>Confirm payment arrangement</li>
                                                                        <li>Update payment status in system</li>
                                                                    </ul>
                                                                </div>

                                                                <p>
                                                                    Best regards,
                                                                    <br/>
                                                                    <span t-out="object.company_id.name">Your Company
                                                                    </span>
                                                                </p>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </table>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

    </data>
</odoo>
