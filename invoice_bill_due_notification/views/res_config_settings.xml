<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.invoice.bill.due.notification</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="account.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//block[@id='invoicing_settings']" position="inside">
                <setting id="invoice_bill_due_notification" string="Invoice/Bill Due Notifications"
                         help="Send automatic notifications for due invoices and bills">
                    <field name="enable_invoice_notifications"/>
                    <div class="content-group" invisible="not enable_invoice_notifications">
                        <div class="row mt16">
                            <label for="invoice_due_notification_days" class="col-lg-3 o_light_label" string="Days Before Due"/>
                            <field name="invoice_due_notification_days"/>
                        </div>
                        <div class="row mt8">
                            <label for="invoice_notification_email" class="col-lg-3 o_light_label" string="Emails"/>
                            <field name="invoice_notification_email"/>
                        </div>
                    </div>
                </setting>
            </xpath>

            <xpath expr="//block[@id='account_vendor_bills']" position="inside">
                <setting id="bill_due_notification" string="Bill Due Notifications"
                         help="Send automatic notifications for due vendor bills">
                    <field name="enable_bill_notifications"/>
                    <div class="content-group" invisible="not enable_bill_notifications">
                        <div class="row mt16">
                            <label for="bill_due_notification_days" class="col-lg-3 o_light_label" string="Days Before Due"/>
                            <field name="bill_due_notification_days"/>
                        </div>
                        <div class="row mt8">
                            <label for="bill_notification_email" class="col-lg-3 o_light_label" string="Emails"/>
                            <field name="bill_notification_email"/>
                        </div>
                    </div>
                </setting>
            </xpath>
        </field>
    </record>
</odoo>