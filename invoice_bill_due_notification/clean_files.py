import os

def clean_null_bytes(directory):
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'rb') as f:
                        content = f.read()
                    
                    if b'\x00' in content:
                        print(f"Cleaning: {filepath}")
                        clean_content = content.replace(b'\x00', b'')
                        with open(filepath, 'wb') as f:
                            f.write(clean_content)
                except Exception as e:
                    print(f"Error processing {filepath}: {e}")

clean_null_bytes('.')