# Invoice / Bill Due Notification Module

## Overview
This module provides automated email notifications for invoice and bill due dates in Odoo 18. It helps payables teams stay on top of upcoming due dates with configurable notification settings.

## Features

### 🔔 Dual Notification System
- **Configurable Days**: Set custom notification days (e.g., 7 days, 8 days before due date)
- **One Day Alert**: Automatic urgent notification 1 day before due date
- **Separate Processing**: Independent cron jobs for invoices and bills

### 📧 Professional Email Templates
- **Invoice Notifications**: Professional templates for customer invoice due dates
- **Bill Notifications**: Dedicated templates for vendor bill payments
- **Urgent Alerts**: Special formatting for one-day-before notifications
- **Company Branding**: Includes company logo and contact information

### ⚙️ Configuration Options
- **Enable/Disable**: Toggle notifications for invoices and bills separately
- **Custom Days**: Configure notification days before due date
- **Email Recipients**: Set multiple email addresses for notifications
- **Automatic Tracking**: Prevents duplicate notifications

## Installation

1. Copy the module to your Odoo addons directory
2. Update the app list in Odoo
3. Install the "NI - Invoice / Bill Due Notification" module

## Configuration

### Settings Location
Go to: **Settings > Accounting > Invoice/Bill Due Notifications**

### Configuration Options

#### Invoice Notifications
- **Enable Invoice Due Notifications**: Toggle to enable/disable
- **Invoice Due Notification Days**: Number of days before due date (default: 7)
- **Invoice Notification Email**: Comma-separated email addresses

#### Bill Notifications
- **Enable Bill Due Notifications**: Toggle to enable/disable
- **Bill Due Notification Days**: Number of days before due date (default: 7)
- **Bill Notification Email**: Comma-separated email addresses

### Example Configuration
```
Invoice Due Notification Days: 7
Invoice Notification Email: <EMAIL>, <EMAIL>

Bill Due Notification Days: 5
Bill Notification Email: <EMAIL>, <EMAIL>
```

## How It Works

### Cron Jobs
The module creates two automated cron jobs:

1. **Invoice Due Date Notifications**
   - Runs daily at 8:00 AM
   - Processes customer invoices (`out_invoice`)
   - Sends notifications based on configured days + 1 day before

2. **Bill Due Date Notifications**
   - Runs daily at 8:30 AM
   - Processes vendor bills (`in_invoice`)
   - Sends notifications based on configured days + 1 day before

### Filtering Criteria
Notifications are sent for invoices/bills that meet ALL criteria:
- ✅ Posted state (`state = 'posted'`)
- ✅ Unpaid or partially paid (`payment_state in ['not_paid', 'partial']`)
- ✅ Due date matches notification date
- ✅ Notification not already sent (tracked automatically)

### Notification Reset
Notification flags are automatically reset when:
- Invoice/bill is marked as paid
- Payment state changes to 'paid'

## Email Templates

### Template Types
1. **Invoice Due Notification**: Standard reminder for customer invoices
2. **Invoice Due Tomorrow**: Urgent alert for invoices due next day
3. **Bill Due Notification**: Standard reminder for vendor bills
4. **Bill Due Tomorrow**: Urgent alert for bills due next day

### Template Features
- 📊 **Detailed Information**: Invoice/bill number, partner, dates, amounts
- 🎨 **Professional Design**: Clean, branded email layout
- ⚠️ **Urgency Indicators**: Special formatting for urgent notifications
- 📱 **Responsive**: Works on desktop and mobile devices

## Technical Details

### Models
- `invoice.bill.notification`: Core notification logic
- `account.move`: Extended with notification tracking fields
- `res.config.settings`: Configuration interface

### Key Fields Added to account.move
- `due_notification_sent`: Boolean flag for regular notifications
- `due_notification_sent_date`: Timestamp of notification
- `one_day_notification_sent`: Boolean flag for urgent notifications
- `one_day_notification_sent_date`: Timestamp of urgent notification

### Cron Job Methods
- `_cron_send_invoice_due_notifications()`: Process invoice notifications
- `_cron_send_bill_due_notifications()`: Process bill notifications

## Testing

### Manual Testing
You can manually trigger notifications for testing:

```python
# In Odoo shell or debug mode
env['invoice.bill.notification']._cron_send_invoice_due_notifications()
env['invoice.bill.notification']._cron_send_bill_due_notifications()
```

### Test Scenarios
1. Create test invoices/bills with due dates
2. Configure notification settings
3. Run cron jobs manually
4. Verify email notifications are sent
5. Check notification flags are updated

## Troubleshooting

### Common Issues

#### No Notifications Sent
- ✅ Check if notifications are enabled in settings
- ✅ Verify email addresses are configured
- ✅ Ensure invoices/bills meet filtering criteria
- ✅ Check cron jobs are active

#### Duplicate Notifications
- ✅ Module automatically prevents duplicates
- ✅ Notification flags reset when payment is made

#### Email Template Issues
- ✅ Verify email templates exist in Email Templates menu
- ✅ Check template configuration and syntax

### Logs
Check Odoo logs for notification processing information:
- Success messages: "Sent X invoice/bill due notifications"
- Error messages: Detailed error information for troubleshooting

## Support

For technical support or customization requests, please contact your Odoo administrator or development team.

## Version History

- **v18.*********: Initial release for Odoo 18
  - Dual notification system for invoices and bills
  - Configurable notification days
  - Professional email templates
  - Automatic duplicate prevention
