# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

import logging
from datetime import datetime, timedelta
from odoo import api, fields, models, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class AccountMove(models.Model):
    _inherit = 'account.move'

    # Notification tracking fields
    due_notification_sent = fields.Boolean(
        string='Due Notification Sent',
        default=False,
        help='Indicates if the due date notification has been sent for this invoice/bill'
    )

    due_notification_sent_date = fields.Datetime(
        string='Due Notification Sent Date',
        help='Date when the due date notification was sent'
    )

    one_day_notification_sent = fields.Boolean(
        string='One Day Notification Sent',
        default=False,
        help='Indicates if the one day before due date notification has been sent'
    )

    one_day_notification_sent_date = fields.Datetime(
        string='One Day Notification Sent Date',
        help='Date when the one day before due date notification was sent'
    )

    def reset_notification_flags(self):
        """Reset notification flags when invoice/bill is paid or cancelled"""
        self.write({
            'due_notification_sent': False,
            'due_notification_sent_date': False,
            'one_day_notification_sent': False,
            'one_day_notification_sent_date': False,
        })

    def write(self, vals):
        """Reset notification flags when payment state changes to paid"""
        res = super(AccountMove, self).write(vals)

        # Reset notification flags when invoice/bill is paid
        if 'payment_state' in vals and vals['payment_state'] == 'paid':
            self.reset_notification_flags()

        return res

    @api.model
    def _cron_send_invoice_due_notifications(self):
        """Cron job to send invoice due date notifications"""
        try:
            self._send_due_notifications('invoice')
        except Exception as e:
            _logger.error("Error in invoice due notification cron: %s", str(e))

    @api.model
    def _cron_send_bill_due_notifications(self):
        """Cron job to send bill due date notifications"""
        try:
            self._send_due_notifications('bill')
        except Exception as e:
            _logger.error("Error in bill due notification cron: %s", str(e))

    @api.model
    def _send_due_notifications(self, notification_type):
        """Send due date notifications for invoices or bills"""

        # Check if notifications are enabled
        if notification_type == 'invoice':
            enabled = self.env['ir.config_parameter'].sudo().get_param(
                'invoice_bill_due_notification.enable_invoice_notifications', 'True'
            ) == 'True'
            notification_days = int(self.env['ir.config_parameter'].sudo().get_param(
                'invoice_bill_due_notification.invoice_due_notification_days', '7'
            ))
            move_types = ['out_invoice']
            template_ref = 'invoice_bill_due_notification.email_template_invoice_due_notification'
            one_day_template_ref = 'invoice_bill_due_notification.email_template_invoice_one_day_due'
        else:
            enabled = self.env['ir.config_parameter'].sudo().get_param(
                'invoice_bill_due_notification.enable_bill_notifications', 'True'
            ) == 'True'
            notification_days = int(self.env['ir.config_parameter'].sudo().get_param(
                'invoice_bill_due_notification.bill_due_notification_days', '7'
            ))
            move_types = ['in_invoice']
            template_ref = 'invoice_bill_due_notification.email_template_bill_due_notification'
            one_day_template_ref = 'invoice_bill_due_notification.email_template_bill_one_day_due'

        if not enabled:
            _logger.info(f"{notification_type.title()} due notifications are disabled")
            return

        # Calculate target dates
        today = fields.Date.today()
        notification_date = today + timedelta(days=notification_days)
        one_day_before_date = today + timedelta(days=1)

        # Send regular due date notifications
        self._send_notifications_for_date(
            move_types, notification_date, template_ref, 'due_notification_sent', notification_type
        )

        # Send one day before due date notifications
        self._send_notifications_for_date(
            move_types, one_day_before_date, one_day_template_ref, 'one_day_notification_sent', notification_type
        )

    @api.model
    def _send_notifications_for_date(self, move_types, target_date, template_ref, flag_field, notification_type):
        """Send notifications for specific date and move types"""

        # Find moves that need notification
        domain = [
            ('move_type', 'in', move_types),
            ('state', '=', 'posted'),
            ('payment_state', 'in', ['not_paid', 'partial']),
            ('invoice_date_due', '=', target_date),
            (flag_field, '=', False),
        ]

        moves = self.env['account.move'].search(domain)

        if not moves:
            _logger.info(f"No {notification_type}s found for notification on {target_date}")
            return

        # Get email template
        try:
            template = self.env.ref(template_ref)
        except ValueError:
            _logger.error(f"Email template {template_ref} not found")
            return

        # Send notifications
        notification_count = 0
        for move in moves:
            try:
                template.send_mail(move.id, force_send=True)

                # Update notification flag
                move.write({
                    flag_field: True,
                    f"{flag_field}_date": fields.Datetime.now(),
                })

                notification_count += 1
                _logger.info(f"Sent {notification_type} due notification for {move.name}")

            except Exception as e:
                _logger.error(f"Failed to send {notification_type} notification for {move.name}: {str(e)}")

        _logger.info(f"Sent {notification_count} {notification_type} due notifications for {target_date}")

