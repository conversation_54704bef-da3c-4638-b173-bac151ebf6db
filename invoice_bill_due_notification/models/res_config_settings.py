# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import fields, models


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    # Invoice Due Notification Settings
    invoice_due_notification_days = fields.Integer(
        string='Invoice Due Notification Days',
        default=7,
        config_parameter='invoice_bill_due_notification.invoice_due_notification_days',
        help='Number of days before invoice due date to send notification email'
    )
    
    invoice_notification_email = fields.Char(
        string='Invoice Notification Email',
        config_parameter='invoice_bill_due_notification.invoice_notification_email',
        help='Email address to send invoice due notifications (comma-separated for multiple emails)'
    )
    
    # Bill Due Notification Settings
    bill_due_notification_days = fields.Integer(
        string='Bill Due Notification Days',
        default=7,
        config_parameter='invoice_bill_due_notification.bill_due_notification_days',
        help='Number of days before bill due date to send notification email'
    )
    
    bill_notification_email = fields.Char(
        string='Bill Notification Email',
        config_parameter='invoice_bill_due_notification.bill_notification_email',
        help='Email address to send bill due notifications (comma-separated for multiple emails)'
    )
    
    # Enable/Disable Notifications
    enable_invoice_notifications = fields.Boolean(
        string='Enable Invoice Due Notifications',
        default=True,
        config_parameter='invoice_bill_due_notification.enable_invoice_notifications',
        help='Enable automatic email notifications for invoice due dates'
    )
    
    enable_bill_notifications = fields.Boolean(
        string='Enable Bill Due Notifications',
        default=True,
        config_parameter='invoice_bill_due_notification.enable_bill_notifications',
        help='Enable automatic email notifications for bill due dates'
    )
